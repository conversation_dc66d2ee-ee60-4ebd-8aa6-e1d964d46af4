# Scraper-2 - <PERSON><PERSON>list Lead Generation Tool

A Python-based tool that scrapes Craigs<PERSON> for potential business leads and generates personalized outreach emails using AI. This tool helps identify small businesses that might need website development services and creates targeted outreach emails for them.

## 🎯 Overview

This project automates the lead generation process in two main phases:

1. **🔍 Scraping Phase**: Scrapes Craigslist posts and extracts business information using AI
2. **📧 Email Generation Phase**: Creates personalized outreach emails for potential clients

## 📁 Project Structure

```
scraper-2/
├── scripts/                    # 🚀 Executable scripts
│   ├── scrape_craigslist.py   # Main scraping script
│   ├── smart-gen.py           # Email generation script
│   ├── verify_ai.py           # AI connectivity test
│   └── dev.py                 # Development utility
├── lead_scraper/              # 📦 Core scraping package
│   ├── __init__.py            # Package exports
│   ├── extractor.py           # AI extraction and data processing
│   └── scraper_utils.py       # Scraping utilities and regex patterns
├── data/                      # 📄 Sample data and examples
│   ├── craigslist_html/       # Sample HTML files for testing
│   └── example/               # Example data files
├── output/                    # 📤 Generated files (gitignored)
│   ├── prospects.json         # Current prospects for email generation
│   ├── emails/                # Generated email files
│   └── scrapes/               # Backup scrapes by date
├── tests/                     # 🧪 Unit tests
├── docs/                      # 📚 Documentation
├── .env                       # 🔐 Environment variables (create this)
├── .gitignore                 # 🚫 Git ignore rules
└── requirements.txt           # 📋 Python dependencies
```

## 🚀 Quick Start

### 1. Prerequisites

- Python 3.8 or higher
- Git (for cloning the repository)
- A Hugging Face account and API token

### 2. Installation

1. **Clone the repository:**

   ```bash
   git clone <repository-url>
   cd scraper-2
   ```

2. **Install dependencies:**

   ```bash
   pip install -r requirements.txt
   ```

3. **Install Playwright browsers (first time only):**

   ```bash
   python -m playwright install chromium
   ```

4. **Create `.env` file in the root directory:**
   ```bash
   # Copy and fill in your actual token
   HF_TOKEN=your_huggingface_token
   HF_MODEL=Qwen/Qwen2.5-7B-Instruct  # optional, defaults to this model
   ```

### 3. Quick Test

**Option A: Use the Desktop GUI (Recommended)**

```bash
python gui_app.py
# or
python run_gui.py
```

**Option B: Use Command Line**

1. **Test AI connectivity:**

   ```bash
   cd scripts
   python verify_ai.py
   ```

2. **Run demo scrape (using local HTML):**

   ```bash
   python scrape_craigslist.py --local-html --limit 1
   ```

3. **Generate emails:**
   ```bash
   python smart-gen.py
   ```

## 📖 Detailed Usage

### 🖥️ **Desktop GUI (Recommended)**

The easiest way to use the tool is through the desktop interface:

```bash
python gui_app.py
```

**GUI Features:**

- **🔍 Scraping Tab**: Configure and run scraping with real-time progress
- **📧 Email Generation Tab**: Load prospects, preview and generate emails
- **👥 Prospects Tab**: Review and manage your prospect database
- **⚙️ Settings Tab**: Configure API keys, models, and preferences
- **✨ High DPI Support**: Crisp, clear interface on high-resolution displays

### 💻 **Command Line Interface**

All scripts are located in the `scripts/` directory. Always run them from there:

```bash
cd scripts
```

### 🔍 Scraping Commands

**Live Craigslist scraping:**

```bash
# Scrape Chicago general services, limit to 25 posts
python scrape_craigslist.py --city chicago --category bbb --limit 25

# Scrape multiple categories
python scrape_craigslist.py --city newyork --category lbs --limit 10
```

**Test scraping (using local HTML files):**

```bash
# Safe testing without hitting live Craigslist
python scrape_craigslist.py --local-html --limit 1
```

### 📧 Email Generation

```bash
# Generate emails from prospects.json
python smart-gen.py
```

### 🛠️ Development Utilities

```bash
# Run tests
python dev.py test

# Clean cache files
python dev.py clean

# Test AI connection
python dev.py verify

# Setup project (install dependencies)
python dev.py setup
```

## 🏷️ Craigslist Category Codes

| Code  | Category          | Description                         |
| ----- | ----------------- | ----------------------------------- |
| `bbb` | General services  | Business services, consulting, etc. |
| `lbs` | Labor/moving      | Moving services, labor, handyman    |
| `bts` | Beauty/wellness   | Salons, spas, fitness               |
| `aos` | Automotive        | Auto repair, detailing              |
| `crs` | Creative services | Design, photography, marketing      |
| `evs` | Event services    | Catering, DJ, event planning        |

## 📂 Output Files

- **`output/prospects.json`** - Current prospects ready for email generation
- **`output/scrapes/MMDDYYYY/`** - Dated backup archives of all scrapes
- **`output/emails/`** - Generated email files (one per prospect)

## 🧪 Testing

Run the test suite to ensure everything works correctly:

```bash
cd scripts
python dev.py test
```

Or run tests directly:

```bash
python -m unittest tests/test_extractor.py
```

## 🔧 Development

The project uses a clean package structure. Import from `lead_scraper`:

```python
from lead_scraper import analyze_post, build_prospect_record
from lead_scraper.scraper_utils import extract_deterministic
```

## 🚨 Important Notes

- **Rate Limiting**: The scraper includes built-in delays to be respectful to Craigslist
- **Local Testing**: Always test with `--local-html` first before live scraping
- **AI Costs**: Be mindful of API usage when processing large numbers of posts
- **Legal**: Ensure compliance with Craigslist's terms of service and local laws

## 🔧 Troubleshooting

### GUI Issues

**Blurry or fuzzy interface on Windows:**

- The application includes automatic DPI awareness fixes for high-resolution displays
- If you still experience blurriness, try running as administrator or updating your display drivers

**GUI won't start:**

- Ensure you're running from the project root directory
- Check that all dependencies are installed: `pip install -r requirements.txt`
- Verify your Python installation includes tkinter: `python -c "import tkinter"`

**Import errors:**

- Make sure you're in the correct directory: `cd scraper-2`
- Check that the `lead_scraper` package exists and is properly structured

## 📚 Documentation

- See `docs/commands.md` for additional command reference
- Check the `data/example/` folder for sample data formats

## 📄 License

This project is for educational and business development purposes. Please use responsibly and in compliance with all applicable terms of service and laws.
