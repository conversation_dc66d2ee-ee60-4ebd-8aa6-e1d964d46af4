import json
import os
import sys

# Add parent directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from dotenv import load_dotenv
from huggingface_hub import InferenceClient

# -------- CONFIG --------
# Get the project root directory (parent of scripts directory)
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
INPUT_FILE = os.path.join(project_root, "output", "prospects.json")   # JSON with client info
OUTPUT_DIR = os.path.join(project_root, "output", "emails")           # folder for email text files
MODEL_NAME = "Qwen/Qwen2.5-7B-Instruct"  # Qwen model for email generation

# Load Hugging Face token from .env
load_dotenv()
HF_TOKEN = os.getenv("HF_TOKEN")

# -------- INITIALIZE CLIENT --------
print("Initializing Hugging Face Inference Client...")
client = InferenceClient(token=HF_TOKEN)

# -------- AI PROMPT TEMPLATE --------
SYSTEM_PROMPT = """You are an expert email copywriter. Your goal is to write a personalized outreach email for a small business owner based on their Craigslist post. The purpose of this email is to find clients for SonataSites, a company that builds or fixes websites. Do not use any em dashes in the email.

Instructions:
- Use the prospect's name, business_name (if it exists), service_offered, location, and the Craigslist post body.
- When referencing their Craigslist post, create a clean, professional 6-word maximum summary of their services instead of copying the raw post title. For example: "offering your multi-faceted handyman services" instead of "👍️$30/HR! MOVING LABOR, HANDYMAN, LANDSCAPERS, MOVERS + MORE!️👍"
- Include a note about their website status based on the provided information.
- Make the email sound human, smooth, and varied. Avoid repetition.
- Keep the email professional but friendly and personable.
- Format the response exactly like this:

post_link: [post_link]
phone_number: [phone_number]
email to send to: [emails]

subject: Hi [name], responding to your post on craigslist

[Body]

Best regards,
Alex L.
https://sonatasites.com"""

def create_user_prompt(client_data):
    emails = ", ".join(client_data.get("emails", []))
    name = client_data.get("name", "there")
    business_name = client_data.get("business_name", "")
    service_offered = client_data.get("service_offered", "")
    location = client_data.get("location", "")
    post_body = client_data.get("post_body", "")
    post_link = client_data.get("post_link", "")
    has_website = client_data.get("has_website", False)
    website_is_working = client_data.get("website_is_working", True)

    # Website status message
    if has_website:
        if website_is_working:
            website_message = "I also noticed your website and can help make it even more effective for attracting clients."
        else:
            website_message = "I noticed your website has some issues — I can help fix it quickly."
    else:
        website_message = "I can help you set up a professional website to showcase your services."

    return f"""
Craigslist post:
{post_body}

Prospect info:
Name: {name}
Business Name: {business_name}
Service Offered: {service_offered}
Location: {location}
Post Link: {post_link}
Emails: {emails}
Website message to include: {website_message}

Please write a personalized outreach email for this prospect.
"""

# -------- HELPER FUNCTION --------
def generate_email_via_ai(client_data):
    try:
        user_prompt = create_user_prompt(client_data)
        
        # Use chat completions with DeepSeek-V3
        completion = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {
                    "role": "system",
                    "content": SYSTEM_PROMPT
                },
                {
                    "role": "user", 
                    "content": user_prompt
                }
            ],
            max_tokens=800,
            temperature=0.7
        )
        
        return completion.choices[0].message.content.strip()
    
    except Exception as e:
        print(f"Error generating email: {e}")
        return f"Error generating email for {client_data.get('name', 'prospect')}: {str(e)}"

# -------- MAIN FUNCTION --------
def main():
    # Check if HF_TOKEN is available
    if not HF_TOKEN:
        print("Error: HF_TOKEN not found in environment variables.")
        print("Please add your Hugging Face token to your .env file:")
        print("HF_TOKEN=your_token_here")
        return

    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # Load prospects JSON
    try:
        with open(INPUT_FILE, "r", encoding="utf-8") as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: {INPUT_FILE} not found.")
        return
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in {INPUT_FILE}")
        return

    prospects = data.get("prospects", {})

    if not prospects:
        print("No prospects found in the JSON file.")
        return

    print(f"Found {len(prospects)} prospects to process...")

    # Generate AI emails for each client
    for client_id, client_data in prospects.items():
        client_name = client_data.get('name', client_id)
        print(f"Generating email for {client_name}...")
        
        email_text = generate_email_via_ai(client_data)

        filename = os.path.join(OUTPUT_DIR, f"{client_id}.txt")
        with open(filename, "w", encoding="utf-8") as f:
            f.write(email_text)

        print(f"Saved -> {filename}")

    print("Email generation complete!")

if __name__ == "__main__":
    main()