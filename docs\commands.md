## See what AIs are installed on computer.

- `python -m huggingface_hub.commands.huggingface_cli scan-cache --verbose`

## Setup

- Create and fill .env

  - HF_TOKEN=your_hf_token
  - (optional) HF_MODEL=Qwen/Qwen2.5-7B-Instruct

- Install Python deps

  - pip install -r requirements.txt

- Install Playwright browsers (first time only)
  - python -m playwright install chromium

## Project structure

- lead_scraper/
  - **init**.py (exports extractor API)
  - extractor.py (AI extraction and merging logic)
  - scraper_utils.py (deterministic regex + helpers)
- scripts/
  - scrape_craigslist.py (CLI for scraping)
  - smart-gen.py (email generation from prospects.json)
  - verify_ai.py (sanity check for HF API)

New code should import from `lead_scraper`, e.g., `from lead_scraper import analyze_post`.

## Verify AI connectivity

- python verify_ai.py

## Scrape (live)

- python scrape_craigslist.py --city chicago --category bbb --limit 25 --model Qwen/Qwen2.5-7B-Instruct

## Scrape (dry-run using local HTML)

- python scrape_craigslist.py --local-html --limit 1

Outputs:

- prospects.json (overwritten)
- scrapes/MMDDYYYY/<city-category-limit>.json
