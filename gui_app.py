#!/usr/bin/env python3
"""
Craigslist Lead Generator - Desktop GUI Application
A Tkinter-based interface for the lead generation tool.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import json
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Fix for blurry GUI on Windows high DPI displays
def fix_dpi_awareness():
    """Fix DPI awareness for crisp GUI on high DPI displays"""
    try:
        from ctypes import windll
        # Try the newer method first (Windows 10 version 1703+)
        try:
            windll.shcore.SetProcessDpiAwareness(2)  # PROCESS_PER_MONITOR_DPI_AWARE_V2
        except:
            try:
                windll.shcore.SetProcessDpiAwareness(1)  # PROCESS_PER_MONITOR_DPI_AWARE
            except:
                windll.user32.SetProcessDPIAware()  # Fallback for older Windows
    except:
        pass  # Not on Windows or ctypes not available

# Apply DPI fix before creating any GUI elements
fix_dpi_awareness()

# Add the current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dotenv import load_dotenv

# Import our existing modules
try:
    from lead_scraper import analyze_post, build_prospect_record
    from lead_scraper.scraper_utils import extract_deterministic
except ImportError as e:
    print(f"Error importing lead_scraper modules: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

load_dotenv()

class LeadGeneratorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Craigslist Lead Generator v2.0")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)

        # Configure style for better appearance
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # Configure fonts for better readability on high DPI
        self.configure_fonts()
        
        # Variables
        self.scraping_active = False
        self.prospects_data = []

        # Load settings first
        self.load_settings()

        # Create main interface
        self.create_widgets()

    def configure_fonts(self):
        """Configure fonts for better readability on high DPI displays"""
        try:
            # Get the default font and increase size slightly for better readability
            default_font = self.root.option_get("font", "TkDefaultFont")
            if default_font:
                # Parse font info and increase size
                font_family = "Segoe UI" if os.name == 'nt' else "Arial"
                font_size = 9
                self.root.option_add("*Font", f"{font_family} {font_size}")
        except:
            pass  # Use system defaults if font configuration fails

    def create_widgets(self):
        """Create the main GUI widgets"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_scraping_tab()
        self.create_email_tab()
        self.create_prospects_tab()
        self.create_settings_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_scraping_tab(self):
        """Create the scraping interface tab"""
        scraping_frame = ttk.Frame(self.notebook)
        self.notebook.add(scraping_frame, text="🔍 Scraping")
        
        # Configuration section
        config_frame = ttk.LabelFrame(scraping_frame, text="Scraping Configuration", padding=10)
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # City and Category selection
        row1 = ttk.Frame(config_frame)
        row1.pack(fill=tk.X, pady=5)
        
        ttk.Label(row1, text="City:").pack(side=tk.LEFT)
        self.city_var = tk.StringVar(value="chicago")
        self.city_combo = ttk.Combobox(row1, textvariable=self.city_var, width=15)
        self.city_combo['values'] = ('chicago', 'newyork', 'losangeles', 'miami', 'boston', 'seattle', 'denver')
        self.city_combo.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(row1, text="Category:").pack(side=tk.LEFT)
        self.category_var = tk.StringVar(value="bbb")
        self.category_combo = ttk.Combobox(row1, textvariable=self.category_var, width=15)
        self.category_combo['values'] = ('bbb', 'lbs', 'bts', 'aos', 'crs', 'evs')
        self.category_combo.pack(side=tk.LEFT, padx=5)
        
        # Limit and options
        row2 = ttk.Frame(config_frame)
        row2.pack(fill=tk.X, pady=5)
        
        ttk.Label(row2, text="Limit:").pack(side=tk.LEFT)
        self.limit_var = tk.StringVar(value="25")
        self.limit_entry = ttk.Entry(row2, textvariable=self.limit_var, width=10)
        self.limit_entry.pack(side=tk.LEFT, padx=(5, 20))
        
        self.local_html_var = tk.BooleanVar()
        self.local_html_check = ttk.Checkbutton(row2, text="Use Local HTML (Testing)", 
                                               variable=self.local_html_var)
        self.local_html_check.pack(side=tk.LEFT, padx=5)
        
        # Control buttons
        button_frame = ttk.Frame(scraping_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.start_button = ttk.Button(button_frame, text="Start Scraping", 
                                      command=self.start_scraping, style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="Stop", 
                                     command=self.stop_scraping, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.clear_button = ttk.Button(button_frame, text="Clear Results", 
                                      command=self.clear_results)
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        # Progress section
        progress_frame = ttk.LabelFrame(scraping_frame, text="Progress", padding=10)
        progress_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.progress_var = tk.StringVar(value="Ready to start scraping")
        ttk.Label(progress_frame, textvariable=self.progress_var).pack(anchor=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # Results section
        results_frame = ttk.LabelFrame(scraping_frame, text="Results", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=15, wrap=tk.WORD)
        self.results_text.pack(fill=tk.BOTH, expand=True)
        
    def create_email_tab(self):
        """Create the email generation tab"""
        email_frame = ttk.Frame(self.notebook)
        self.notebook.add(email_frame, text="📧 Email Generation")

        # Load prospects section
        load_frame = ttk.LabelFrame(email_frame, text="Load Prospects", padding=10)
        load_frame.pack(fill=tk.X, padx=10, pady=5)

        load_row = ttk.Frame(load_frame)
        load_row.pack(fill=tk.X)

        self.prospects_file_var = tk.StringVar(value="output/prospects.json")
        ttk.Label(load_row, text="Prospects File:").pack(side=tk.LEFT)
        self.prospects_entry = ttk.Entry(load_row, textvariable=self.prospects_file_var, width=40)
        self.prospects_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        ttk.Button(load_row, text="Browse", command=self.browse_prospects_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(load_row, text="Load", command=self.load_prospects).pack(side=tk.LEFT, padx=5)

        # Prospects list and email preview
        content_frame = ttk.Frame(email_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Left side - prospects list
        left_frame = ttk.LabelFrame(content_frame, text="Prospects", padding=5)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # Prospects listbox with scrollbar
        list_frame = ttk.Frame(left_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        self.prospects_listbox = tk.Listbox(list_frame, selectmode=tk.SINGLE)
        prospects_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.prospects_listbox.yview)
        self.prospects_listbox.config(yscrollcommand=prospects_scrollbar.set)

        self.prospects_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        prospects_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.prospects_listbox.bind('<<ListboxSelect>>', self.on_prospect_select)

        # Prospect info
        info_frame = ttk.Frame(left_frame)
        info_frame.pack(fill=tk.X, pady=5)

        self.prospect_info_var = tk.StringVar(value="Select a prospect to view details")
        ttk.Label(info_frame, textvariable=self.prospect_info_var, wraplength=300).pack(anchor=tk.W)

        # Right side - email preview and generation
        right_frame = ttk.LabelFrame(content_frame, text="Email Generation", padding=5)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # Email generation controls
        gen_controls = ttk.Frame(right_frame)
        gen_controls.pack(fill=tk.X, pady=(0, 5))

        self.generate_single_btn = ttk.Button(gen_controls, text="Generate Email", command=self.generate_email)
        self.generate_single_btn.pack(side=tk.LEFT, padx=5)

        self.generate_all_btn = ttk.Button(gen_controls, text="Generate All", command=self.generate_all_emails)
        self.generate_all_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(gen_controls, text="Save Email", command=self.save_current_email).pack(side=tk.LEFT, padx=5)

        # Progress bar for batch generation
        self.email_progress_frame = ttk.Frame(right_frame)
        self.email_progress_frame.pack(fill=tk.X, pady=5)

        self.email_progress_label = ttk.Label(self.email_progress_frame, text="")
        self.email_progress_label.pack(anchor=tk.W)

        self.email_progress_bar = ttk.Progressbar(self.email_progress_frame, mode='determinate')
        self.email_progress_bar.pack(fill=tk.X, pady=2)

        # Initially hide progress bar
        self.email_progress_frame.pack_forget()

        # Email preview
        self.email_preview = scrolledtext.ScrolledText(right_frame, height=18, wrap=tk.WORD)
        self.email_preview.pack(fill=tk.BOTH, expand=True)

        # Status for email generation
        self.email_status_var = tk.StringVar(value="Load prospects to begin")
        ttk.Label(email_frame, textvariable=self.email_status_var).pack(side=tk.BOTTOM, fill=tk.X, padx=10)
        
    def create_prospects_tab(self):
        """Create the prospects review tab"""
        prospects_frame = ttk.Frame(self.notebook)
        self.notebook.add(prospects_frame, text="👥 Prospects")
        
        # Will implement this next
        ttk.Label(prospects_frame, text="Prospects Review Interface - Coming Soon", 
                 font=("Arial", 14)).pack(expand=True)
        
    def create_settings_tab(self):
        """Create the settings tab"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="⚙️ Settings")

        # Create scrollable frame
        canvas = tk.Canvas(settings_frame)
        scrollbar = ttk.Scrollbar(settings_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # AI Configuration
        ai_frame = ttk.LabelFrame(scrollable_frame, text="AI Configuration", padding=10)
        ai_frame.pack(fill=tk.X, padx=10, pady=5)

        # HF Token
        token_row = ttk.Frame(ai_frame)
        token_row.pack(fill=tk.X, pady=5)
        ttk.Label(token_row, text="Hugging Face Token:").pack(anchor=tk.W)
        self.hf_token_var = tk.StringVar(value=self.hf_token)
        self.hf_token_entry = ttk.Entry(token_row, textvariable=self.hf_token_var, show="*", width=50)
        self.hf_token_entry.pack(fill=tk.X, pady=2)

        # HF Model
        model_row = ttk.Frame(ai_frame)
        model_row.pack(fill=tk.X, pady=5)
        ttk.Label(model_row, text="AI Model:").pack(anchor=tk.W)
        self.hf_model_var = tk.StringVar(value=self.hf_model)
        self.hf_model_combo = ttk.Combobox(model_row, textvariable=self.hf_model_var, width=47)
        self.hf_model_combo['values'] = (
            'Qwen/Qwen2.5-7B-Instruct',
            'deepseek-ai/DeepSeek-V3-0324',
            'microsoft/DialoGPT-medium',
            'meta-llama/Llama-2-7b-chat-hf'
        )
        self.hf_model_combo.pack(fill=tk.X, pady=2)

        # Scraping Configuration
        scraping_frame = ttk.LabelFrame(scrollable_frame, text="Scraping Configuration", padding=10)
        scraping_frame.pack(fill=tk.X, padx=10, pady=5)

        # Default settings
        defaults_row1 = ttk.Frame(scraping_frame)
        defaults_row1.pack(fill=tk.X, pady=5)

        ttk.Label(defaults_row1, text="Default City:").pack(side=tk.LEFT)
        self.default_city_var = tk.StringVar(value=self.default_city)
        default_city_combo = ttk.Combobox(defaults_row1, textvariable=self.default_city_var, width=15)
        default_city_combo['values'] = ('chicago', 'newyork', 'losangeles', 'miami', 'boston', 'seattle', 'denver')
        default_city_combo.pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(defaults_row1, text="Default Category:").pack(side=tk.LEFT)
        self.default_category_var = tk.StringVar(value=self.default_category)
        default_category_combo = ttk.Combobox(defaults_row1, textvariable=self.default_category_var, width=15)
        default_category_combo['values'] = ('bbb', 'lbs', 'bts', 'aos', 'crs', 'evs')
        default_category_combo.pack(side=tk.LEFT, padx=5)

        # Rate limiting
        rate_row = ttk.Frame(scraping_frame)
        rate_row.pack(fill=tk.X, pady=5)

        ttk.Label(rate_row, text="Rate Limit (seconds):").pack(side=tk.LEFT)
        ttk.Label(rate_row, text="Min:").pack(side=tk.LEFT, padx=(20, 5))
        self.rate_min_var = tk.StringVar(value=self.rate_min)
        ttk.Entry(rate_row, textvariable=self.rate_min_var, width=8).pack(side=tk.LEFT, padx=5)

        ttk.Label(rate_row, text="Max:").pack(side=tk.LEFT, padx=(10, 5))
        self.rate_max_var = tk.StringVar(value=self.rate_max)
        ttk.Entry(rate_row, textvariable=self.rate_max_var, width=8).pack(side=tk.LEFT, padx=5)

        # Output Configuration
        output_frame = ttk.LabelFrame(scrollable_frame, text="Output Configuration", padding=10)
        output_frame.pack(fill=tk.X, padx=10, pady=5)

        # Output directory
        output_row = ttk.Frame(output_frame)
        output_row.pack(fill=tk.X, pady=5)
        ttk.Label(output_row, text="Output Directory:").pack(anchor=tk.W)
        self.output_dir_var = tk.StringVar(value=self.output_dir)
        output_entry_frame = ttk.Frame(output_row)
        output_entry_frame.pack(fill=tk.X, pady=2)
        ttk.Entry(output_entry_frame, textvariable=self.output_dir_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(output_entry_frame, text="Browse", command=self.browse_output_dir).pack(side=tk.RIGHT, padx=(5, 0))

        # Email template settings
        email_frame = ttk.LabelFrame(scrollable_frame, text="Email Configuration", padding=10)
        email_frame.pack(fill=tk.X, padx=10, pady=5)

        # Sender name
        sender_row = ttk.Frame(email_frame)
        sender_row.pack(fill=tk.X, pady=5)
        ttk.Label(sender_row, text="Sender Name:").pack(anchor=tk.W)
        self.sender_name_var = tk.StringVar(value=self.sender_name)
        ttk.Entry(sender_row, textvariable=self.sender_name_var, width=30).pack(anchor=tk.W, pady=2)

        # Company website
        website_row = ttk.Frame(email_frame)
        website_row.pack(fill=tk.X, pady=5)
        ttk.Label(website_row, text="Company Website:").pack(anchor=tk.W)
        self.company_website_var = tk.StringVar(value=self.company_website)
        ttk.Entry(website_row, textvariable=self.company_website_var, width=40).pack(anchor=tk.W, pady=2)

        # Buttons
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=20)

        ttk.Button(button_frame, text="Save Settings", command=self.save_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Reset to Defaults", command=self.reset_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Test AI Connection", command=self.test_ai_connection).pack(side=tk.LEFT, padx=5)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def load_settings(self):
        """Load settings from .env file"""
        # Initialize default values first
        self.hf_token = os.getenv("HF_TOKEN", "")
        self.hf_model = os.getenv("HF_MODEL", "Qwen/Qwen2.5-7B-Instruct")

        # Load other settings with defaults
        self.default_city = os.getenv("DEFAULT_CITY", "chicago")
        self.default_category = os.getenv("DEFAULT_CATEGORY", "bbb")
        self.rate_min = os.getenv("RATE_LIMIT_MIN", "1.5")
        self.rate_max = os.getenv("RATE_LIMIT_MAX", "4.0")
        self.output_dir = os.getenv("OUTPUT_DIR", "output")
        self.sender_name = os.getenv("SENDER_NAME", "Alex L.")
        self.company_website = os.getenv("COMPANY_WEBSITE", "https://sonatasites.com")
    
    def start_scraping(self):
        """Start the scraping process in a separate thread"""
        if self.scraping_active:
            return
            
        # Validate inputs
        try:
            limit = int(self.limit_var.get())
            if limit <= 0:
                raise ValueError("Limit must be positive")
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid positive number for limit")
            return
            
        if not self.hf_token:
            messagebox.showerror("Error", "Please configure HF_TOKEN in Settings")
            return
        
        # Update UI
        self.scraping_active = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar['value'] = 0
        self.results_text.delete(1.0, tk.END)
        
        # Start scraping in separate thread
        self.scraping_thread = threading.Thread(target=self.run_scraping, daemon=True)
        self.scraping_thread.start()
    
    def run_scraping(self):
        """Run the actual scraping process"""
        try:
            city = self.city_var.get()
            category = self.category_var.get()
            limit = int(self.limit_var.get())
            use_local = self.local_html_var.get()
            
            self.update_status(f"Starting scraping: {city} {category} (limit: {limit})")
            self.log_result(f"🚀 Starting scraping session")
            self.log_result(f"📍 City: {city}, Category: {category}, Limit: {limit}")
            self.log_result(f"🔧 Mode: {'Local HTML' if use_local else 'Live Craigslist'}")
            self.log_result("-" * 50)
            
            if use_local:
                self.run_local_scraping()
            else:
                self.run_live_scraping(city, category, limit)
                
        except Exception as e:
            self.log_result(f"❌ Error: {str(e)}")
            messagebox.showerror("Scraping Error", f"An error occurred: {str(e)}")
        finally:
            self.scraping_finished()
    
    def run_local_scraping(self):
        """Run scraping using local HTML files"""
        # This is a simplified version - you'd integrate with your existing local HTML logic
        self.log_result("🔍 Using local HTML files for testing...")
        
        # Simulate progress
        for i in range(5):
            if not self.scraping_active:
                break
            self.root.after(0, lambda: self.progress_bar.config(value=(i+1)*20))
            self.root.after(0, lambda i=i: self.update_status(f"Processing local file {i+1}/5"))
            threading.Event().wait(1)  # Simulate work
        
        self.log_result("✅ Local scraping completed")
        self.log_result("📊 Found 3 test prospects")
    
    def run_live_scraping(self, city, category, limit):
        """Run live Craigslist scraping using the actual scraping logic"""
        import asyncio

        self.log_result("🌐 Starting live Craigslist scraping...")
        self.log_result(f"📍 Target: {city}.craigslist.org/{category}")

        try:
            # Run the async scraping function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            prospects = loop.run_until_complete(
                self.scrape_craigslist_async(city, category, limit)
            )

            loop.close()

            # Save results
            self.save_scraping_results(prospects, city, category, limit)

            self.log_result(f"✅ Live scraping completed: {len(prospects)} prospects found")

            # Show completion notification
            self.root.after(0, lambda: messagebox.showinfo("Scraping Complete",
                f"Successfully scraped {len(prospects)} prospects!\n\n"
                f"Results saved to output/prospects.json\n"
                f"You can now switch to the Email Generation tab to create emails."))

        except Exception as e:
            self.log_result(f"❌ Live scraping failed: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("Scraping Error", f"Live scraping failed: {str(e)}"))

    async def scrape_craigslist_async(self, city, category, limit):
        """Async scraping function using the actual scraping logic"""
        # Import the scraping function from the scripts directory
        import sys
        import os

        # Add scripts directory to path
        scripts_dir = os.path.join(os.path.dirname(__file__), 'scripts')
        if scripts_dir not in sys.path:
            sys.path.insert(0, scripts_dir)

        # Import the scraping function
        from scrape_craigslist import scrape

        # Update progress periodically
        self.root.after(0, lambda: self.update_status(f"Connecting to {city}.craigslist.org..."))
        self.root.after(0, lambda: self.progress_bar.config(value=10))

        # Log progress
        self.root.after(0, lambda: self.log_result(f"🔍 Collecting post links from {city}.craigslist.org/{category}"))

        # Run the scraping
        model = os.getenv("HF_MODEL")
        self.root.after(0, lambda: self.progress_bar.config(value=30))

        result = await scrape(city, category, limit, model)

        # Extract prospects list from the result
        prospects_dict = result.get("prospects", {})
        prospects_list = list(prospects_dict.values())

        # Update progress
        self.root.after(0, lambda: self.progress_bar.config(value=90))
        self.root.after(0, lambda: self.update_status(f"Processing {len(prospects_list)} prospects..."))
        self.root.after(0, lambda: self.log_result(f"🎯 Found {len(prospects_list)} valid prospects"))

        return prospects_list

    def save_scraping_results(self, prospects, city, category, limit):
        """Save scraping results to files"""
        try:
            # Create the payload structure expected by save_outputs
            prospects_dict = {prospect.get('id', f'prospect_{i}'): prospect
                            for i, prospect in enumerate(prospects)}

            payload = {
                "meta": {
                    "city": city,
                    "category": category,
                    "limit": limit,
                    "count": len(prospects),
                    "scraped_at": datetime.now().astimezone().isoformat(),
                },
                "prospects": prospects_dict,
            }

            # Import save function
            import sys
            import os
            scripts_dir = os.path.join(os.path.dirname(__file__), 'scripts')
            if scripts_dir not in sys.path:
                sys.path.insert(0, scripts_dir)

            from scrape_craigslist import save_outputs

            # Save the results
            output_path = save_outputs(payload, city, category, limit)

            self.log_result(f"💾 Results saved to: {output_path}")
            self.log_result(f"📄 Main prospects file: output/prospects.json")

            # Update the prospects data in the GUI and refresh the display
            self.prospects_data = prospects
            self.root.after(0, self.refresh_prospects_display)

        except Exception as e:
            self.log_result(f"❌ Failed to save results: {str(e)}")

    def refresh_prospects_display(self):
        """Refresh the prospects display after scraping"""
        if self.prospects_data:
            # Update status
            email_dir = os.path.join("output", "emails")
            existing_emails = 0
            if os.path.exists(email_dir):
                existing_emails = len([f for f in os.listdir(email_dir) if f.endswith('.txt')])

            status_msg = f"Loaded {len(self.prospects_data)} prospects from scraping"
            if existing_emails > 0:
                status_msg += f" ({existing_emails} emails already exist)"

            self.email_status_var.set(status_msg)

            # Update prospects listbox
            self.prospects_listbox.delete(0, tk.END)
            for prospect in self.prospects_data:
                name = prospect.get('name', 'Unknown')
                business = prospect.get('business_name', '')
                title = prospect.get('title', '')

                # Create display name with fallbacks
                if name and name != 'Unknown' and name.strip():
                    display_name = name
                elif business and business.strip():
                    display_name = business
                elif title and title.strip():
                    display_name = title
                else:
                    display_name = f"Prospect {prospect.get('id', 'Unknown')}"

                if business and business.strip() and business != display_name:
                    display_name += f" - {business}"

                # Check if email exists for this prospect
                prospect_id = prospect.get('id', '')
                if prospect_id:
                    email_filepath = os.path.join("output", "emails", f"email_{prospect_id}.txt")
                    if os.path.exists(email_filepath):
                        display_name = "✉️ " + display_name  # Add email icon

                self.prospects_listbox.insert(tk.END, display_name)

    def stop_scraping(self):
        """Stop the scraping process"""
        self.scraping_active = False
        self.update_status("Stopping scraping...")
        self.log_result("🛑 Scraping stopped by user")
    
    def scraping_finished(self):
        """Called when scraping is finished"""
        self.scraping_active = False
        self.root.after(0, lambda: self.start_button.config(state=tk.NORMAL))
        self.root.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
        self.root.after(0, lambda: self.update_status("Scraping finished"))
    
    def clear_results(self):
        """Clear the results display"""
        self.results_text.delete(1.0, tk.END)
        self.progress_bar['value'] = 0
        self.update_status("Results cleared")
    
    def log_result(self, message):
        """Add a message to the results display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.root.after(0, lambda: self.results_text.insert(tk.END, f"[{timestamp}] {message}\n"))
        self.root.after(0, lambda: self.results_text.see(tk.END))
    
    def update_status(self, message):
        """Update the status bar"""
        self.root.after(0, lambda: self.status_var.set(message))

    # Email Generation Methods
    def browse_prospects_file(self):
        """Browse for prospects file"""
        filename = filedialog.askopenfilename(
            title="Select Prospects File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialdir="output"
        )
        if filename:
            self.prospects_file_var.set(filename)

    def load_prospects(self):
        """Load prospects from JSON file"""
        try:
            filepath = self.prospects_file_var.get()
            if not os.path.exists(filepath):
                messagebox.showerror("Error", f"File not found: {filepath}")
                return

            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Handle different JSON structures
            if isinstance(data, list):
                # Simple array of prospects
                self.prospects_data = data
            elif isinstance(data, dict):
                if 'prospects' in data:
                    # Nested structure: {"prospects": {"id1": {...}, "id2": {...}}}
                    prospects_dict = data['prospects']
                    self.prospects_data = list(prospects_dict.values())
                else:
                    # Flat dictionary structure: {"id1": {...}, "id2": {...}}
                    self.prospects_data = list(data.values())
            else:
                raise ValueError("Unsupported JSON structure")

            # Filter out non-dictionary items (in case of mixed data)
            self.prospects_data = [p for p in self.prospects_data if isinstance(p, dict)]

            # Update prospects listbox
            self.prospects_listbox.delete(0, tk.END)
            for prospect in self.prospects_data:
                name = prospect.get('name', 'Unknown')
                business = prospect.get('business_name', '')
                title = prospect.get('title', '')

                # Create display name with fallbacks
                if name and name != 'Unknown' and name.strip():
                    display_name = name
                elif business and business.strip():
                    display_name = business
                elif title and title.strip():
                    display_name = title
                else:
                    display_name = f"Prospect {prospect.get('id', 'Unknown')}"

                if business and business.strip() and business != display_name:
                    display_name += f" - {business}"

                # Check if email exists for this prospect
                prospect_id = prospect.get('id', '')
                if prospect_id:
                    email_filepath = os.path.join("output", "emails", f"email_{prospect_id}.txt")
                    if os.path.exists(email_filepath):
                        display_name = "✉️ " + display_name  # Add email icon

                self.prospects_listbox.insert(tk.END, display_name)

            # Check existing emails
            email_dir = os.path.join("output", "emails")
            existing_emails = 0
            if os.path.exists(email_dir):
                existing_emails = len([f for f in os.listdir(email_dir) if f.endswith('.txt')])

            status_msg = f"Loaded {len(self.prospects_data)} prospects"
            if existing_emails > 0:
                status_msg += f" ({existing_emails} emails already exist)"

            self.email_status_var.set(status_msg)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load prospects: {str(e)}")

    def on_prospect_select(self, event):
        """Handle prospect selection"""
        selection = self.prospects_listbox.curselection()
        if not selection:
            return

        index = selection[0]
        prospect = self.prospects_data[index]

        # Update prospect info
        info = f"Name: {prospect.get('name', 'N/A')}\n"
        info += f"Business: {prospect.get('business_name', 'N/A')}\n"
        info += f"Service: {prospect.get('service_offered', 'N/A')}\n"
        info += f"Location: {prospect.get('location', 'N/A')}\n"
        info += f"Email: {', '.join(prospect.get('emails', []))}\n"
        info += f"Phone: {prospect.get('phone_number', 'N/A')}\n"
        info += f"Website: {prospect.get('website', 'N/A')}"

        self.prospect_info_var.set(info)

        # Check if email already exists for this prospect
        self.load_existing_email_for_prospect(prospect)

    def load_existing_email_for_prospect(self, prospect):
        """Load existing email file for the selected prospect if it exists"""
        try:
            prospect_id = prospect.get('id', '')
            if not prospect_id:
                self.email_preview.delete(1.0, tk.END)
                self.email_preview.insert(tk.END, "Click 'Generate Email' to create email for this prospect")
                return

            # Check for existing email file
            email_filename = f"email_{prospect_id}.txt"
            email_filepath = os.path.join("output", "emails", email_filename)

            if os.path.exists(email_filepath):
                # Load and display existing email
                with open(email_filepath, 'r', encoding='utf-8') as f:
                    email_content = f.read()

                self.email_preview.delete(1.0, tk.END)
                self.email_preview.insert(tk.END, email_content)

                # Update status to show it's a loaded email
                self.email_status_var.set(f"Loaded existing email for {prospect.get('name') or prospect.get('business_name') or prospect.get('title') or 'prospect'}")
            else:
                # No existing email found
                self.email_preview.delete(1.0, tk.END)
                self.email_preview.insert(tk.END, "Click 'Generate Email' to create email for this prospect")
                self.email_status_var.set("No existing email found - ready to generate new email")

        except Exception as e:
            # Error loading email
            self.email_preview.delete(1.0, tk.END)
            self.email_preview.insert(tk.END, f"Error loading email: {str(e)}\n\nClick 'Generate Email' to create a new email")
            self.email_status_var.set("Error loading existing email")

    def generate_email(self):
        """Generate email for selected prospect"""
        selection = self.prospects_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a prospect first")
            return

        index = selection[0]
        prospect = self.prospects_data[index]

        # Check if email already exists
        prospect_id = prospect.get('id', '')
        email_filename = f"email_{prospect_id}.txt"
        email_filepath = os.path.join("output", "emails", email_filename)

        if os.path.exists(email_filepath):
            result = messagebox.askyesno("Email Exists",
                "An email already exists for this prospect.\n\n"
                "Do you want to regenerate it?")
            if not result:
                return

        # Show generating message
        self.email_preview.delete(1.0, tk.END)
        self.email_preview.insert(tk.END, "Generating email... Please wait...")
        self.root.update()

        try:
            # Generate email content
            email_content = self.create_email_content(prospect)

            # Display generated email
            self.email_preview.delete(1.0, tk.END)
            self.email_preview.insert(tk.END, email_content)

            self.email_status_var.set("Email generated successfully")

        except Exception as e:
            self.email_preview.delete(1.0, tk.END)
            self.email_preview.insert(tk.END, f"Error generating email: {str(e)}")
            self.email_status_var.set("Email generation failed")

    def create_email_content(self, prospect):
        """Create email content for a prospect (placeholder)"""
        # This is a simplified version - you'd integrate with your smart-gen.py logic

        # Extract prospect information with better fallbacks
        name = prospect.get('name', '').strip()
        business = prospect.get('business_name', '').strip()
        title = prospect.get('title', '').strip()
        service = prospect.get('service_offered', '').strip()
        location = prospect.get('location', '').strip()
        emails = prospect.get('emails', [])
        phone = prospect.get('phone_number', '').strip()
        post_link = prospect.get('post_link', '')
        post_body = prospect.get('post_body', '')

        # Determine the best name to use
        if name:
            greeting_name = name
        elif business:
            greeting_name = "there"  # Use generic greeting if only business name
        elif title:
            greeting_name = "there"
        else:
            greeting_name = "there"

        # Determine service description
        if service:
            service_desc = service
        elif business:
            service_desc = f"{business} services"
        elif title:
            service_desc = title
        else:
            # Try to extract service from post body
            service_desc = "your services"

        # Create location reference
        location_ref = f" in {location}" if location else ""

        # Format emails
        email_list = ', '.join(emails) if emails else "No email found"

        subject = f"Hi {greeting_name}, responding to your post on craigslist"

        # Create personalized body
        business_ref = f"{business} " if business else ""

        body = f"""Hi {greeting_name},

I saw your post on Craigslist about {service_desc}. {business_ref}Your business looks great!

I help small businesses like yours get more customers through professional websites. Many of my clients{location_ref} have seen great results.

Would you be interested in a quick chat about how a website could help grow your business?

Best regards,
Alex L.
https://sonatasites.com"""

        email_content = f"""post_link: {post_link}
phone_number: {phone}
email to send to: {email_list}

subject: {subject}

{body}"""

        return email_content

    def generate_all_emails(self):
        """Generate emails for all prospects"""
        if not self.prospects_data:
            messagebox.showwarning("Warning", "No prospects loaded")
            return

        result = messagebox.askyesno("Confirm",
                                   f"Generate emails for all {len(self.prospects_data)} prospects?\n\n"
                                   f"This will create {len(self.prospects_data)} email files in the output/emails directory.")
        if not result:
            return

        # Start batch generation in a separate thread
        self.batch_generation_thread = threading.Thread(target=self.run_batch_generation, daemon=True)
        self.batch_generation_thread.start()

    def run_batch_generation(self):
        """Run batch email generation in background thread"""
        try:
            total_prospects = len(self.prospects_data)
            generated_count = 0
            failed_count = 0

            # Show progress bar and disable buttons
            self.root.after(0, self.start_batch_generation_ui)

            # Ensure output directory exists
            output_dir = os.path.join("output", "emails")
            os.makedirs(output_dir, exist_ok=True)

            for i, prospect in enumerate(self.prospects_data):
                try:
                    # Update progress with prospect info
                    progress_percent = ((i + 1) / total_prospects) * 100
                    prospect_name = (prospect.get('name') or prospect.get('business_name') or
                                   prospect.get('title') or f"Prospect {i+1}").strip()
                    progress_msg = f"Generating email {i+1}/{total_prospects}: {prospect_name}"

                    self.root.after(0, lambda p=progress_percent, msg=progress_msg: self.update_batch_progress(p, msg))

                    # Generate email content
                    email_content = self.create_email_content(prospect)

                    # Create filename
                    prospect_id = prospect.get('id', f'prospect_{i+1}')
                    filename = f"email_{prospect_id}.txt"
                    filepath = os.path.join(output_dir, filename)

                    # Save email to file
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(email_content)

                    generated_count += 1

                    # Small delay to prevent overwhelming the system
                    threading.Event().wait(0.2)

                except Exception as e:
                    failed_count += 1
                    print(f"Failed to generate email for prospect {i+1}: {str(e)}")

            # Update final status and hide progress
            success_msg = f"Batch generation complete! Generated {generated_count} emails"
            if failed_count > 0:
                success_msg += f" ({failed_count} failed)"

            self.root.after(0, lambda: self.finish_batch_generation(success_msg, generated_count, failed_count, output_dir))

        except Exception as e:
            error_msg = f"Batch generation failed: {str(e)}"
            self.root.after(0, lambda: self.finish_batch_generation_error(error_msg))

    def start_batch_generation_ui(self):
        """Update UI when starting batch generation"""
        self.email_progress_frame.pack(fill=tk.X, pady=5, before=self.email_preview)
        self.email_progress_bar['value'] = 0
        self.generate_all_btn.config(state=tk.DISABLED)
        self.generate_single_btn.config(state=tk.DISABLED)

    def update_batch_progress(self, percent, message):
        """Update progress bar and message"""
        self.email_progress_bar['value'] = percent
        self.email_progress_label.config(text=message)
        self.email_status_var.set(message)

    def finish_batch_generation(self, message, generated_count, failed_count, output_dir):
        """Finish batch generation successfully"""
        self.email_progress_frame.pack_forget()
        self.generate_all_btn.config(state=tk.NORMAL)
        self.generate_single_btn.config(state=tk.NORMAL)
        self.email_status_var.set(message)

        result = messagebox.askyesno("Batch Generation Complete",
            f"Successfully generated {generated_count} emails!\n"
            f"Files saved to: {output_dir}\n"
            f"Failed: {failed_count}\n\n"
            f"Would you like to open the output folder?")

        if result:
            self.open_output_folder(output_dir)

    def finish_batch_generation_error(self, error_msg):
        """Finish batch generation with error"""
        self.email_progress_frame.pack_forget()
        self.generate_all_btn.config(state=tk.NORMAL)
        self.generate_single_btn.config(state=tk.NORMAL)
        self.email_status_var.set(error_msg)
        messagebox.showerror("Batch Generation Error", error_msg)

    def open_output_folder(self, folder_path):
        """Open the output folder in file explorer"""
        try:
            import subprocess
            import platform

            if platform.system() == "Windows":
                os.startfile(folder_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", folder_path])
            else:  # Linux
                subprocess.run(["xdg-open", folder_path])
        except Exception as e:
            messagebox.showerror("Error", f"Could not open folder: {str(e)}")

    def save_current_email(self):
        """Save the current email to file"""
        email_content = self.email_preview.get(1.0, tk.END).strip()
        if not email_content or email_content == "Click 'Generate Email' to create email for this prospect":
            messagebox.showwarning("Warning", "No email to save")
            return

        # Get selected prospect for filename
        selection = self.prospects_listbox.curselection()
        if selection:
            prospect = self.prospects_data[selection[0]]
            prospect_id = prospect.get('id', 'unknown')
            filename = f"email_{prospect_id}.txt"

            # Check if file already exists in default location
            default_filepath = os.path.join("output", "emails", filename)
            if os.path.exists(default_filepath):
                result = messagebox.askyesno("File Exists",
                    f"Email file already exists:\n{default_filepath}\n\n"
                    "Do you want to overwrite it?")
                if result:
                    # Save directly to existing location
                    try:
                        with open(default_filepath, 'w', encoding='utf-8') as f:
                            f.write(email_content)
                        messagebox.showinfo("Success", f"Email updated: {default_filepath}")
                        return
                    except Exception as e:
                        messagebox.showerror("Error", f"Failed to save email: {str(e)}")
                        return
        else:
            filename = f"email_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        # Show save dialog
        filepath = filedialog.asksaveasfilename(
            title="Save Email",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            initialdir="output/emails",
            initialfile=filename
        )

        if filepath:
            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(email_content)
                messagebox.showinfo("Success", f"Email saved to {filepath}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save email: {str(e)}")

    # Settings Methods
    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory", initialdir=self.output_dir_var.get())
        if directory:
            self.output_dir_var.set(directory)

    def save_settings(self):
        """Save settings to .env file"""
        try:
            env_content = f"""# Craigslist Lead Generator Settings
HF_TOKEN={self.hf_token_var.get()}
HF_MODEL={self.hf_model_var.get()}

# Default scraping settings
DEFAULT_CITY={self.default_city_var.get()}
DEFAULT_CATEGORY={self.default_category_var.get()}
RATE_LIMIT_MIN={self.rate_min_var.get()}
RATE_LIMIT_MAX={self.rate_max_var.get()}

# Output settings
OUTPUT_DIR={self.output_dir_var.get()}

# Email settings
SENDER_NAME={self.sender_name_var.get()}
COMPANY_WEBSITE={self.company_website_var.get()}
"""

            with open('.env', 'w', encoding='utf-8') as f:
                f.write(env_content)

            # Update instance variables
            self.hf_token = self.hf_token_var.get()
            self.hf_model = self.hf_model_var.get()

            messagebox.showinfo("Success", "Settings saved successfully!")
            self.update_status("Settings saved")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")

    def reset_settings(self):
        """Reset settings to defaults"""
        result = messagebox.askyesno("Confirm", "Reset all settings to defaults?")
        if result:
            self.hf_token_var.set("")
            self.hf_model_var.set("Qwen/Qwen2.5-7B-Instruct")
            self.default_city_var.set("chicago")
            self.default_category_var.set("bbb")
            self.rate_min_var.set("1.5")
            self.rate_max_var.set("4.0")
            self.output_dir_var.set("output")
            self.sender_name_var.set("Alex L.")
            self.company_website_var.set("https://sonatasites.com")
            self.update_status("Settings reset to defaults")

    def test_ai_connection(self):
        """Test AI connection"""
        if not self.hf_token_var.get():
            messagebox.showerror("Error", "Please enter HF_TOKEN first")
            return

        try:
            # This would integrate with your verify_ai.py logic
            self.update_status("Testing AI connection...")

            # Simulate test for now
            import time
            time.sleep(1)

            messagebox.showinfo("Success", "AI connection test successful!")
            self.update_status("AI connection OK")

        except Exception as e:
            messagebox.showerror("Error", f"AI connection failed: {str(e)}")
            self.update_status("AI connection failed")

def main():
    """Main entry point"""
    root = tk.Tk()
    app = LeadGeneratorGUI(root)
    
    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
