import json
import os
import re
from typing import Dict, Any, List, Optional

from dotenv import load_dotenv
from huggingface_hub import InferenceClient

# Centralized config and extractor logic

DEFAULT_MODEL = os.getenv("HF_MODEL", "Qwen/Qwen2.5-7B-Instruct")

load_dotenv()
HF_TOKEN = os.getenv("HF_TOKEN")

_client: Optional[InferenceClient] = None

def get_client() -> InferenceClient:
    global _client
    if _client is None:
        _client = InferenceClient(token=HF_TOKEN)
    return _client

SYSTEM_PROMPT = (
    "You are a data extraction assistant. Given a Craigslist post title and body, "
    "return a STRICT JSON object with keys: name (string), business_name (string), "
    "service_offered (string), location (string), emails (array of strings), "
    "phone_number (string), website (string, absolute URL if present, else empty), has_website (boolean), is_prospect (boolean). "
    "IMPORTANT: For service_offered, extract a clear description of what services they provide (e.g., 'junk removal and demolition', 'moving services', 'concrete and stucco work'). "
    "For location, look for city names, neighborhoods, or service areas mentioned in the post. "
    "A 'prospect' is a small business/professional offering services to the public "
    "(e.g., movers, cleaners, tutors, laborers, beauticians) and not a software/online service or web dev agency. "
    "If you are unsure about a field, return an empty string or empty array. "
    "Do not include any commentary, only the JSON object. "
    "Do not recommend any prospects who are advertising coding related services or people who may already know how to build a website due to their expertise in tech"
)

# Utility: robustly parse first JSON object in a text response
_def_obj_braces = re.compile(r"{.*}", re.DOTALL)

def _extract_json_object(text: str) -> Optional[Dict[str, Any]]:
    # Try to find the first JSON object by brace matching
    # Simple fallback: regex from first '{' to last '}'
    if not text:
        return None
    # Try to isolate a JSON object
    m = _def_obj_braces.search(text)
    candidate = m.group(0) if m else text
    try:
        return json.loads(candidate)
    except Exception:
        # Try to fix common trailing commas or single quotes
        fixed = candidate.replace("'", '"')
        fixed = re.sub(r",\s*([}\]])", r"\1", fixed)
        try:
            return json.loads(fixed)
        except Exception:
            return None

# Simple merger: prefer deterministic regex values when present; otherwise AI

def merge_fields(base: Dict[str, Any], ai: Dict[str, Any]) -> Dict[str, Any]:
    out = {}
    # Fields of interest
    fields = [
        "name",
        "business_name",
        "service_offered",
        "location",
        "emails",
        "phone_number",
        "website",
        "has_website",
        "is_prospect",
    ]

    for f in fields:
        base_val = base.get(f)
        ai_val = ai.get(f)
        if f == "emails":
            # Merge unique emails
            emails: List[str] = []
            if isinstance(base_val, list):
                emails.extend(base_val)
            if isinstance(ai_val, list):
                emails.extend(ai_val)
            out[f] = sorted({e.strip() for e in emails if e and isinstance(e, str)})
        elif f == "has_website":
            out[f] = bool(base_val) or bool(ai_val)
        elif f == "phone_number":
            out[f] = base_val if base_val else (ai_val or "")
        elif f == "website":
            # prefer deterministic website when present; else AI; ensure string
            out[f] = base_val if isinstance(base_val, str) and base_val else (ai_val if isinstance(ai_val, str) else "")
        else:
            # general fallback
            out[f] = base_val if base_val not in (None, "") else (ai_val if ai_val is not None else "")

    return out


def analyze_post(title: str, body: str, url: str, deterministic: Dict[str, Any],
                 model: str = DEFAULT_MODEL) -> Dict[str, Any]:
    """
    Call the HF model to extract fields, then merge with deterministic findings.
    deterministic keys can include: emails (list), phone_number (str), has_website (bool).
    """
    client = get_client()

    user_prompt = (
        f"Title: {title}\n\n"
        f"Body: {body}\n\n"
        f"URL: {url}\n\n"
        "Extract information and return JSON ONLY with these keys:\n"
        "- name: person's name if mentioned\n"
        "- business_name: company/business name if mentioned\n"
        "- service_offered: clear description of services (e.g., 'junk removal', 'moving services', 'concrete work')\n"
        "- location: city, neighborhood, or service area mentioned\n"
        "- emails: array of email addresses found\n"
        "- phone_number: phone number if found\n"
        "- website: website URL if found\n"
        "- has_website: true if website found, false otherwise\n"
        "- is_prospect: true if this is a service business that could benefit from a website\n"
        "Return only the JSON object, no other text."
    )

    try:
        completion = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": user_prompt},
            ],
            max_tokens=400,
            temperature=0.2,
        )
        content = completion.choices[0].message.content.strip()
        ai_json = _extract_json_object(content) or {}
    except Exception:
        ai_json = {}

    merged = merge_fields(deterministic, ai_json)

    # Ensure required keys exist
    merged.setdefault("name", "")
    merged.setdefault("business_name", "")
    merged.setdefault("service_offered", "")
    merged.setdefault("location", "")
    merged.setdefault("emails", [])
    merged.setdefault("phone_number", "")
    merged.setdefault("website", deterministic.get("website", ""))
    # derive has_website solely from presence of a website URL (deterministic or AI)
    # This avoids false positives where AI sets has_website true without a URL
    merged["has_website"] = bool(merged.get("website") or deterministic.get("website"))
    merged.setdefault("is_prospect", True)  # Treat all contacts as prospects by default

    return merged


def build_prospect_record(post_id: str, url: str, title: str, body: str, fields: Dict[str, Any]) -> Dict[str, Any]:
    """Builds the record in the shape compatible with smart-gen.py plus is_prospect."""
    return {
        "id": post_id,
        "name": fields.get("name", ""),
        "business_name": fields.get("business_name", ""),
        "service_offered": fields.get("service_offered", ""),
        "location": fields.get("location", ""),
        "phone_number": fields.get("phone_number", ""),
        "emails": fields.get("emails", []),
        "post_link": url,
        "post_body": body,
        "website": fields.get("website", ""),
        "has_website": bool(fields.get("has_website", False)) or bool(fields.get("website")),
        "website_is_working": False,  # explicitly ignored downstream
        "is_prospect": bool(fields.get("is_prospect", True)),  # Default to True
        "title": title,
    }

