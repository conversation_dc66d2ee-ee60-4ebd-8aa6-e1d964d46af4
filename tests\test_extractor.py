import json
import unittest

from lead_scraper.extractor import merge_fields
from lead_scraper.extractor import _extract_json_object as extract_json


class TestExtractorUtils(unittest.TestCase):
    def test_merge_fields_prefers_deterministic(self):
        base = {
            "emails": ["<EMAIL>"],
            "phone_number": "************",
            "website": "http://example.com",
            "has_website": True,
        }
        ai = {
            "emails": ["<EMAIL>", "<EMAIL>"],
            "phone_number": "************",
            "website": "http://ai-site.com",
            "has_website": False,
        }
        merged = merge_fields(base, ai)
        self.assertEqual(sorted(merged["emails"]), ["<EMAIL>", "<EMAIL>"])
        self.assertEqual(merged["phone_number"], "************")  # deterministic wins
        self.assertEqual(merged["website"], "http://example.com")  # deterministic wins
        self.assertTrue(merged["has_website"])  # true if either true

    def test_extract_json_object_basic(self):
        text = "Here is JSON: {\"a\": 1, \"b\": [2,3]} Thanks!"
        obj = extract_json(text)
        self.assertEqual(obj, {"a": 1, "b": [2, 3]})

    def test_extract_json_object_fallbacks(self):
        text = "{" "'a': 1, 'b': [2,3,],}"  # deliberately broken commas and quotes
        obj = extract_json(text)
        self.assertEqual(obj, {"a": 1, "b": [2, 3]})


if __name__ == "__main__":
    unittest.main()

