#!/usr/bin/env python3
"""
Simple launcher for the Craigslist Lead Generator GUI
"""

import sys
import os

# Fix for blurry GUI on Windows high DPI displays
def fix_dpi_awareness():
    """Fix DPI awareness for crisp GUI on high DPI displays"""
    try:
        from ctypes import windll
        # Try the newer method first (Windows 10 version 1703+)
        try:
            windll.shcore.SetProcessDpiAwareness(2)  # PROCESS_PER_MONITOR_DPI_AWARE_V2
        except:
            try:
                windll.shcore.SetProcessDpiAwareness(1)  # PROCESS_PER_MONITOR_DPI_AWARE
            except:
                windll.user32.SetProcessDPIAware()  # Fallback for older Windows
    except:
        pass  # Not on Windows or ctypes not available

# Apply DPI fix before importing GUI modules
fix_dpi_awareness()

def main():
    """Launch the GUI application"""
    try:
        # Import and run the GUI
        from gui_app import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"Error: {e}")
        print("\nMake sure you have all required dependencies installed:")
        print("pip install -r requirements.txt")
        print("\nAnd that you're running from the project root directory.")
        sys.exit(1)
    except Exception as e:
        print(f"Error launching GUI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
